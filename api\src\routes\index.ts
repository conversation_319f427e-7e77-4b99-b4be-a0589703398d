import { Router, type Request, type Response } from 'express';
import userRoutes from './user_routes';
import customersRoutes from './customer_routes';
import ordersRoutes from './order_routes';
import garmentsRoutes from './garment_routes';
import rawMaterialRoutes from './rawMaterial_routes';

const routes = Router();

routes.get('/', (req: Request, res: Response) =>{
    res.json({message: "Olá! Estou funcionando com TypeScript!"});
});

routes.use('/users', userRoutes);
routes.use('/customers', customersRoutes);
routes.use('/orders', ordersRoutes);
routes.use('/garments', garmentsRoutes);
routes.use('/raw-materials', rawMaterialRoutes);

export default routes;